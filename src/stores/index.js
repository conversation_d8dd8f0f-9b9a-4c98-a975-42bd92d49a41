import { createPinia } from 'pinia'

// Create Pinia instance
export const pinia = createPinia()

// Export stores
export { useAuthStore } from './auth.js'

// Store initialization helper
export const initializeStores = async () => {
  const { useAuthStore } = await import('./auth.js')
  
  const authStore = useAuthStore()
  
  // Initialize stores
  await authStore.initialize()
  
  return {
    authStore
  }
}
