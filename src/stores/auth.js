import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStorage } from '@/composables'
import { API_CONFIG } from '@/utils/constants'
import axios from 'axios'

/**
 * Authentication store using Pinia and Composition API
 */
export const useAuthStore = defineStore('auth', () => {
  // State
  const { token, refreshToken, user, setTokens, setUser, clearAuth, isAuthenticated } = useAuthStorage()
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const isLoggedIn = computed(() => isAuthenticated())
  const currentUser = computed(() => user.value)
  const userRole = computed(() => user.value?.role || USER_ROLES.GUEST)
  const userName = computed(() => {
    if (!user.value) return ''
    return `${user.value.firstName || ''} ${user.value.lastName || ''}`.trim()
  })

  // Actions
  /**
   * Login user with credentials
   * @param {Object} credentials - Login credentials
   * @returns {Promise} Login result
   */
  const login = async (credentials) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/login`, credentials)
      const { accessToken, refreshToken: newRefreshToken, user: userData } = response.data

      setTokens(accessToken, newRefreshToken)
      setUser(userData)

      // Set default authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`

      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Register new user
   * @param {Object} userData - User registration data
   * @returns {Promise} Registration result
   */
  const register = async (userData) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/register`, userData)
      const { accessToken, refreshToken: newRefreshToken, user: newUser } = response.data

      setTokens(accessToken, newRefreshToken)
      setUser(newUser)

      // Set default authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`

      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Registration failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Logout user
   */
  const logout = async () => {
    loading.value = true

    try {
      // Call logout endpoint if token exists
      if (token.value) {
        await axios.post(`${API_CONFIG.BASE_URL}/auth/logout`, {
          refreshToken: refreshToken.value
        })
      }
    } catch (err) {
      console.warn('Logout request failed:', err)
    } finally {
      // Clear local auth data regardless of API call result
      clearAuth()
      delete axios.defaults.headers.common['Authorization']
      loading.value = false
      error.value = null
    }
  }

  /**
   * Refresh authentication token
   * @returns {Promise} Refresh result
   */
  const refreshAuthToken = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/refresh`, {
        refreshToken: refreshToken.value
      })

      const { accessToken, refreshToken: newRefreshToken } = response.data
      setTokens(accessToken, newRefreshToken)

      // Update default authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`

      return response.data
    } catch (err) {
      // If refresh fails, logout user
      await logout()
      throw err
    }
  }

  /**
   * Get current user profile
   * @returns {Promise} User profile
   */
  const fetchUserProfile = async () => {
    if (!token.value) return null

    loading.value = true
    error.value = null

    try {
      const response = await axios.get(`${API_CONFIG.BASE_URL}/auth/profile`)
      setUser(response.data)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch profile'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Update user profile
   * @param {Object} profileData - Profile data to update
   * @returns {Promise} Updated profile
   */
  const updateProfile = async (profileData) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.put(`${API_CONFIG.BASE_URL}/auth/profile`, profileData)
      setUser(response.data)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to update profile'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Change user password
   * @param {Object} passwordData - Password change data
   * @returns {Promise} Change result
   */
  const changePassword = async (passwordData) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.put(`${API_CONFIG.BASE_URL}/auth/change-password`, passwordData)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to change password'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise} Reset request result
   */
  const requestPasswordReset = async (email) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/forgot-password`, { email })
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to request password reset'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Reset password with token
   * @param {Object} resetData - Reset data (token, password)
   * @returns {Promise} Reset result
   */
  const resetPassword = async (resetData) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/reset-password`, resetData)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to reset password'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Check if user has specific role
   * @param {string} role - Role to check
   * @returns {boolean} Has role
   */
  const hasRole = (role) => {
    return userRole.value === role
  }

  /**
   * Check if user has any of the specified roles
   * @param {Array} roles - Roles to check
   * @returns {boolean} Has any role
   */
  const hasAnyRole = (roles) => {
    return roles.includes(userRole.value)
  }

  /**
   * Initialize auth state (call on app startup)
   */
  const initialize = async () => {
    if (token.value) {
      // Set authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      
      // Fetch current user if not already loaded
      if (!user.value) {
        try {
          await fetchUserProfile()
        } catch (err) {
          // If profile fetch fails, clear auth
          await logout()
        }
      }
    }
  }

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    token,
    refreshToken,
    user,
    loading,
    error,
    
    // Getters
    isLoggedIn,
    currentUser,
    userRole,
    userName,
    
    // Actions
    login,
    register,
    logout,
    refreshAuthToken,
    fetchUserProfile,
    updateProfile,
    changePassword,
    requestPasswordReset,
    resetPassword,
    hasRole,
    hasAnyRole,
    initialize,
    clearError
  }
})
