import { createApp } from 'vue'
import App from './App.vue'
import { pinia, initializeStores } from '@/stores'
import axios from 'axios'
import { API_CONFIG } from '@/utils/constants'

// Configure axios defaults
axios.defaults.baseURL = API_CONFIG.BASE_URL

// Create Vue app
const app = createApp(App)

// Use Pinia for state management
app.use(pinia)

// Global error handler
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err, info)

  // You can send errors to a logging service here
  // Example: logError(err, info)
}

// Global warning handler
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Global warning:', msg, trace)
}

// Initialize stores and mount app
initializeStores().then(() => {
  app.mount('#app')
}).catch(error => {
  console.error('Failed to initialize stores:', error)
  app.mount('#app') // Mount anyway to show error state
})
