import { ref, reactive } from 'vue'
import axios from 'axios'

/**
 * Composable for API operations with loading states and error handling
 * @returns {Object} API utilities and state
 */
export function useApi() {
  const loading = ref(false)
  const error = ref(null)
  const data = ref(null)

  /**
   * Generic API request handler
   * @param {Function} apiCall - The API call function
   * @param {Object} options - Request options
   * @returns {Promise} API response
   */
  const execute = async (apiCall, options = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await apiCall(options)
      data.value = response.data
      return response
    } catch (err) {
      error.value = err.response?.data?.message || err.message || 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * GET request
   * @param {string} url - API endpoint
   * @param {Object} config - Axios config
   */
  const get = (url, config = {}) => {
    return execute(() => axios.get(url, config))
  }

  /**
   * POST request
   * @param {string} url - API endpoint
   * @param {Object} payload - Request payload
   * @param {Object} config - Axios config
   */
  const post = (url, payload, config = {}) => {
    return execute(() => axios.post(url, payload, config))
  }

  /**
   * PUT request
   * @param {string} url - API endpoint
   * @param {Object} payload - Request payload
   * @param {Object} config - Axios config
   */
  const put = (url, payload, config = {}) => {
    return execute(() => axios.put(url, payload, config))
  }

  /**
   * DELETE request
   * @param {string} url - API endpoint
   * @param {Object} config - Axios config
   */
  const del = (url, config = {}) => {
    return execute(() => axios.delete(url, config))
  }

  /**
   * Reset state
   */
  const reset = () => {
    loading.value = false
    error.value = null
    data.value = null
  }

  return {
    loading,
    error,
    data,
    execute,
    get,
    post,
    put,
    delete: del,
    reset
  }
}

/**
 * Composable for paginated API requests
 * @param {string} baseUrl - Base API URL
 * @returns {Object} Pagination utilities and state
 */
export function usePaginatedApi(baseUrl) {
  const { loading, error, execute } = useApi()
  
  const items = ref([])
  const pagination = reactive({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  /**
   * Fetch paginated data
   * @param {Object} params - Query parameters
   */
  const fetchPage = async (params = {}) => {
    const queryParams = {
      page: pagination.page,
      limit: pagination.limit,
      ...params
    }

    try {
      const response = await execute(() => 
        axios.get(baseUrl, { params: queryParams })
      )

      items.value = response.data.items || response.data.data || []
      pagination.total = response.data.total || 0
      pagination.totalPages = Math.ceil(pagination.total / pagination.limit)
      
      return response
    } catch (err) {
      items.value = []
      throw err
    }
  }

  /**
   * Go to specific page
   * @param {number} page - Page number
   */
  const goToPage = (page) => {
    pagination.page = page
    return fetchPage()
  }

  /**
   * Go to next page
   */
  const nextPage = () => {
    if (pagination.page < pagination.totalPages) {
      return goToPage(pagination.page + 1)
    }
  }

  /**
   * Go to previous page
   */
  const prevPage = () => {
    if (pagination.page > 1) {
      return goToPage(pagination.page - 1)
    }
  }

  /**
   * Reset pagination
   */
  const reset = () => {
    items.value = []
    pagination.page = 1
    pagination.total = 0
    pagination.totalPages = 0
  }

  return {
    loading,
    error,
    items,
    pagination,
    fetchPage,
    goToPage,
    nextPage,
    prevPage,
    reset
  }
}
