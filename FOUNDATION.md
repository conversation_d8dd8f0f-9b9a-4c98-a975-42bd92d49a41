# Vue Composition API Foundation

This project implements a comprehensive foundation using Vue 3 Composition API, providing reusable composables, base components, utilities, and state management.

## 🏗️ Architecture Overview

The foundation is organized into several key areas:

- **Composables** - Reusable composition functions for common functionality
- **Base Components** - Foundation UI components built with Composition API
- **Utilities** - Helper functions and constants
- **Stores** - Pinia stores using Composition API
- **Types** - TypeScript-like type definitions using JSDoc

## 📁 Project Structure

```
src/
├── composables/           # Reusable composition functions
│   ├── useApi.js         # API operations with loading states
│   ├── useForm.js        # Form handling with validation
│   ├── useStorage.js     # Local/session storage management
│   ├── useNotifications.js # Toast notifications system
│   ├── useUtils.js       # Utility composables
│   └── index.js          # Composables exports
├── components/
│   ├── base/             # Foundation UI components
│   │   ├── BaseButton.vue
│   │   ├── BaseInput.vue
│   │   ├── BaseModal.vue
│   │   ├── BaseCard.vue
│   │   └── index.js
│   └── ExampleFoundation.vue # Demo component
├── stores/               # Pinia stores
│   ├── auth.js          # Authentication store
│   ├── app.js           # Application state store
│   └── index.js         # Store exports
├── utils/               # Utility functions
│   ├── constants.js     # Application constants
│   ├── helpers.js       # Helper functions
│   ├── formatters.js    # Data formatting utilities
│   └── index.js         # Utils exports
└── App.vue              # Main application component
```

## 🧩 Composables

### useApi
Handles API operations with loading states and error handling.

```javascript
import { useApi } from '@/composables'

const api = useApi()

// GET request
await api.get('/users')

// POST request
await api.post('/users', userData)

// Access state
console.log(api.loading.value) // boolean
console.log(api.error.value)   // string | null
console.log(api.data.value)    // any
```

### useForm
Form handling with validation rules.

```javascript
import { useForm, validationRules } from '@/composables'

const form = useForm(
  { email: '', password: '' },
  {
    email: [validationRules.required(), validationRules.email()],
    password: [validationRules.required(), validationRules.minLength(8)]
  }
)

// Handle form submission
const onSubmit = (formData) => {
  console.log('Valid form data:', formData)
}

form.handleSubmit(onSubmit)
```

### useStorage
Reactive local and session storage.

```javascript
import { useLocalStorage, useSessionStorage } from '@/composables'

const { value: theme } = useLocalStorage('theme', 'light')
const { value: token } = useSessionStorage('auth-token', null)

// Values are automatically synced with storage
theme.value = 'dark' // Automatically saved to localStorage
```

### useNotifications
Toast notification system.

```javascript
import { useNotifications } from '@/composables'

const notifications = useNotifications()

notifications.success('Operation completed!')
notifications.error('Something went wrong')
notifications.warning('Please check your input')
notifications.info('New update available')
```

## 🎨 Base Components

### BaseButton
Flexible button component with multiple variants and states.

```vue
<BaseButton 
  variant="primary" 
  size="lg" 
  :loading="isLoading"
  @click="handleClick"
>
  Click me
</BaseButton>
```

### BaseInput
Input component with validation and error handling.

```vue
<BaseInput
  v-model="email"
  label="Email Address"
  type="email"
  :error="emailError"
  placeholder="Enter your email"
  required
/>
```

### BaseModal
Modal component with customizable content.

```vue
<BaseModal v-model="showModal" title="Confirm Action" size="md">
  <p>Are you sure you want to continue?</p>
  
  <template #footer>
    <BaseButton @click="showModal = false">Cancel</BaseButton>
    <BaseButton variant="primary" @click="confirm">Confirm</BaseButton>
  </template>
</BaseModal>
```

### BaseCard
Card component for content organization.

```vue
<BaseCard title="User Profile" subtitle="Manage your account">
  <p>Card content goes here</p>
  
  <template #footer>
    <BaseButton>Edit Profile</BaseButton>
  </template>
</BaseCard>
```

## 🏪 Stores (Pinia)

### Auth Store
Handles user authentication and authorization.

```javascript
import { useAuthStore } from '@/stores'

const authStore = useAuthStore()

// Login
await authStore.login({ email, password })

// Check authentication
if (authStore.isLoggedIn) {
  console.log('User:', authStore.currentUser)
}

// Logout
await authStore.logout()
```

### App Store
Manages global application state.

```javascript
import { useAppStore } from '@/stores'

const appStore = useAppStore()

// Theme management
appStore.toggleTheme()
console.log(appStore.isDarkMode)

// Notifications
appStore.showSuccess('Operation completed!')

// Loading state
appStore.setLoading(true)
```

## 🛠️ Utilities

### Constants
Application-wide constants for configuration.

```javascript
import { API_CONFIG, HTTP_STATUS, THEMES } from '@/utils/constants'

console.log(API_CONFIG.BASE_URL)
console.log(HTTP_STATUS.OK)
console.log(THEMES.DARK)
```

### Helpers
Common utility functions.

```javascript
import { debounce, deepClone, generateUUID } from '@/utils/helpers'

const debouncedFn = debounce(myFunction, 300)
const clonedObj = deepClone(originalObj)
const id = generateUUID()
```

### Formatters
Data formatting utilities.

```javascript
import { formatDate, formatCurrency, formatPhone } from '@/utils/formatters'

const date = formatDate(new Date(), 'long')
const price = formatCurrency(1234.56, 'USD')
const phone = formatPhone('1234567890', 'us')
```

## 🚀 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Start development server:**
   ```bash
   npm run serve
   # or
   yarn serve
   ```

3. **Build for production:**
   ```bash
   npm run build
   # or
   yarn build
   ```

## 💡 Usage Examples

### Creating a New Page Component

```vue
<template>
  <div class="page">
    <BaseCard title="My Page">
      <form @submit.prevent="form.handleSubmit(onSubmit)">
        <BaseInput
          v-model="form.form.name"
          label="Name"
          :error="form.errors.name"
          @blur="form.setFieldTouched('name')"
        />
        
        <BaseButton 
          type="submit" 
          :loading="form.isSubmitting.value"
          :disabled="!form.isValid.value"
        >
          Submit
        </BaseButton>
      </form>
    </BaseCard>
  </div>
</template>

<script>
import { useForm, validationRules } from '@/composables'
import { useAppStore } from '@/stores'
import { BaseCard, BaseInput, BaseButton } from '@/components/base'

export default {
  name: 'MyPage',
  components: { BaseCard, BaseInput, BaseButton },
  
  setup() {
    const appStore = useAppStore()
    
    const form = useForm(
      { name: '' },
      { name: [validationRules.required()] }
    )
    
    const onSubmit = async (data) => {
      try {
        // Handle form submission
        appStore.showSuccess('Form submitted!')
      } catch (error) {
        appStore.showError('Submission failed')
      }
    }
    
    return { form, onSubmit }
  }
}
</script>
```

## 🎯 Best Practices

1. **Use composables for reusable logic** - Extract common functionality into composables
2. **Leverage reactive storage** - Use `useLocalStorage` and `useSessionStorage` for persistent state
3. **Handle loading states** - Always show loading indicators for async operations
4. **Validate forms properly** - Use the validation rules provided by `useForm`
5. **Manage global state** - Use Pinia stores for application-wide state
6. **Follow naming conventions** - Use consistent naming for components and composables
7. **Document your code** - Use JSDoc comments for better developer experience

## 🔧 Customization

The foundation is designed to be easily customizable:

- **Styling**: Modify the CSS classes in base components
- **Themes**: Extend the theme system in the app store
- **Validation**: Add custom validation rules to the form composable
- **API**: Configure API endpoints and behavior in constants
- **Components**: Create new base components following the established patterns

## 📚 Additional Resources

- [Vue 3 Composition API Guide](https://vuejs.org/guide/extras/composition-api-faq.html)
- [Pinia Documentation](https://pinia.vuejs.org/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

This foundation provides a solid starting point for building scalable Vue 3 applications with modern development practices.
